import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:provider/provider.dart';
import 'package:quarterlies/models/models.dart';
import 'package:quarterlies/services/supabase_service.dart';
import 'package:quarterlies/screens/expenses/expense_form_screen.dart';
import 'package:quarterlies/providers/display_settings_provider.dart';
import 'package:quarterlies/providers/loading_state_provider.dart';
import 'package:quarterlies/widgets/loading_widgets.dart';
import 'package:quarterlies/widgets/adaptive_detail_section.dart';
import 'package:quarterlies/widgets/error_display_widgets.dart';
import 'package:quarterlies/widgets/responsive_layout.dart';
import 'package:quarterlies/widgets/responsive_text.dart';
import 'package:quarterlies/utils/responsive_spacing.dart' as spacing;

class ExpenseDetailScreen extends StatefulWidget {
  final String expenseId;

  const ExpenseDetailScreen({super.key, required this.expenseId});

  @override
  State<ExpenseDetailScreen> createState() => _ExpenseDetailScreenState();
}

class _ExpenseDetailScreenState extends State<ExpenseDetailScreen> {
  final SupabaseService _supabaseService = SupabaseService();
  Expense? _expense;
  Job? _job;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _loadExpenseData();
  }

  Future<void> _loadExpenseData() async {
    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'loadExpenseData',
        () async {
          setState(() {
            _errorMessage = null;
          });

          // Get expense details
          final expense = await _supabaseService.getExpenseById(
            widget.expenseId,
          );

          // Get job details
          final job =
              expense.jobId != null
                  ? await _supabaseService.getJobById(expense.jobId!)
                  : null;

          if (!mounted) return;

          setState(() {
            _expense = expense;
            _job = job;
          });
        },
        message: 'Loading expense data...',
        errorMessage: 'Failed to load expense data',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to load expense data: ${e.toString()}';
        });
      }
    }
  }

  Future<void> _deleteExpense() async {
    final confirmed =
        await showDialog<bool>(
          context: context,
          builder:
              (context) => AlertDialog(
                title: const Text('Confirm Delete'),
                content: const Text(
                  'Are you sure you want to delete this expense? This action cannot be undone.',
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.pop(context, false),
                    child: const Text('Cancel'),
                  ),
                  TextButton(
                    onPressed: () => Navigator.pop(context, true),
                    child: const Text(
                      'Delete',
                      style: TextStyle(color: Colors.red),
                    ),
                  ),
                ],
              ),
        ) ??
        false;

    if (!confirmed || !mounted) return;

    final loadingProvider = Provider.of<LoadingStateProvider>(
      context,
      listen: false,
    );

    try {
      await loadingProvider.executeWithLoading(
        'deleteExpense',
        () async {
          setState(() {
            _errorMessage = null;
          });

          await _supabaseService.deleteExpense(widget.expenseId);

          if (mounted) {
            // Show success feedback
            ErrorDisplay.showSuccess(context, 'Expense deleted successfully');
            Navigator.pop(context, true); // Return true to indicate deletion
          }
        },
        message: 'Deleting expense...',
        errorMessage: 'Failed to delete expense',
      );
    } catch (e) {
      if (mounted) {
        setState(() {
          _errorMessage = 'Failed to delete expense: ${e.toString()}';
        });
      }
    }
  }

  Color _getCategoryColor(String? category) {
    if (category == null) return Colors.grey[300]!;

    // Use different colors for different categories
    switch (category) {
      case ExpenseCategory.advertising:
        return Colors.blue[100]!;
      case ExpenseCategory.carAndTruck:
        return Colors.green[100]!;
      case ExpenseCategory.contractLabor:
        return Colors.orange[100]!;
      case ExpenseCategory.officeExpense:
        return Colors.purple[100]!;
      case ExpenseCategory.legalAndProfessional:
        return Colors.red[100]!;
      default:
        return Colors.grey[300]!;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const ResponsiveTitle('Expense Details'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
        elevation: spacing.ResponsiveSpacing.getElevation(context),
        toolbarHeight: spacing.ResponsiveSpacing.getAppBarHeight(context),
        actions: [
          // Edit button
          IconButton(
            icon: Icon(
              Icons.edit,
              size: spacing.ResponsiveSpacing.getIconSize(context),
            ),
            onPressed:
                _expense == null
                    ? null
                    : () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder:
                              (context) => ExpenseFormScreen(expense: _expense),
                        ),
                      ).then((_) => _loadExpenseData());
                    },
          ),
          // Delete button
          IconButton(
            icon: Icon(
              Icons.delete,
              size: spacing.ResponsiveSpacing.getIconSize(context),
            ),
            onPressed: _expense == null ? null : _deleteExpense,
          ),
        ],
      ),
      body: ResponsiveLayout(
        child: Consumer<LoadingStateProvider>(
          builder: (context, loadingProvider, child) {
            final isLoading = loadingProvider.isLoading('loadExpenseData');

            return isLoading
                ? Center(
                  child: QuarterliesLoadingIndicator(
                    message: 'Loading expense data...',
                    size: spacing.ResponsiveSpacing.getIconSize(
                      context,
                      base: 32,
                    ),
                  ),
                )
                : _errorMessage != null
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      ResponsiveBody(
                        _errorMessage!,
                        style: const TextStyle(color: Colors.red),
                      ),
                      SizedBox(
                        height: spacing.ResponsiveSpacing.getSpacing(
                          context,
                          base: 16,
                        ),
                      ),
                      ElevatedButton(
                        onPressed: _loadExpenseData,
                        style: ElevatedButton.styleFrom(
                          minimumSize: Size(
                            120,
                            spacing.ResponsiveSpacing.getButtonHeight(context),
                          ),
                        ),
                        child: const ResponsiveLabel('Retry'),
                      ),
                    ],
                  ),
                )
                : _expense == null
                ? const Center(child: ResponsiveBody('Expense not found'))
                : Consumer<DisplaySettingsProvider>(
                  builder: (context, displayProvider, child) {
                    return SingleChildScrollView(
                      padding: spacing.ResponsiveSpacing.getPadding(
                        context,
                        base: displayProvider.isOfficeMode ? 12 : 16,
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Expense header section
                          AdaptiveDetailSection(
                            title: 'Expense Details',
                            icon: Icons.receipt,
                            alwaysExpandedInOffice: true,
                            children: [
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: ResponsiveTitle(
                                      _expense!.description,
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                  Chip(
                                    label: ResponsiveLabel(
                                      _expense!.category ?? 'Uncategorized',
                                    ),
                                    backgroundColor: _getCategoryColor(
                                      _expense!.category ?? 'Uncategorized',
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(
                                height: spacing.ResponsiveSpacing.getSpacing(
                                  context,
                                  base: displayProvider.isOfficeMode ? 8 : 12,
                                ),
                              ),

                              if (displayProvider.isOfficeMode) ...[
                                // Office Mode: Compact grid layout
                                Row(
                                  children: [
                                    Expanded(
                                      child: AdaptiveInfoRow(
                                        label: 'Amount',
                                        value:
                                            '\$${_expense!.amount.toStringAsFixed(2)}',
                                        icon: Icons.attach_money,
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: AdaptiveInfoRow(
                                        label: 'Date',
                                        value: DateFormat(
                                          'MM/dd/yyyy',
                                        ).format(_expense!.date),
                                        icon: Icons.calendar_today,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 8),
                                if (_job != null)
                                  AdaptiveInfoRow(
                                    label: 'Job',
                                    value: _job!.title,
                                    icon: Icons.work,
                                  ),
                                if (_expense!.isOverhead)
                                  const AdaptiveInfoRow(
                                    label: 'Type',
                                    value: 'Overhead Expense',
                                    icon: Icons.business,
                                  ),
                              ] else ...[
                                // Field Mode: Existing layout
                                ResponsiveBody(
                                  'Job: ${_job?.title ?? (_expense!.isOverhead ? 'Overhead' : 'Unknown')}',
                                ),
                                SizedBox(
                                  height: spacing.ResponsiveSpacing.getSpacing(
                                    context,
                                    base: 16,
                                  ),
                                ),
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    ResponsiveSubtitle(
                                      'Amount: \$${_expense!.amount.toStringAsFixed(2)}',
                                      style: const TextStyle(
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    ResponsiveBody(
                                      'Date: ${DateFormat('MM/dd/yyyy').format(_expense!.date)}',
                                    ),
                                  ],
                                ),
                              ],
                            ],
                          ),

                          // Tags section
                          if (_expense!.tags != null &&
                              _expense!.tags!.isNotEmpty)
                            AdaptiveDetailSection(
                              title: 'Tags',
                              icon: Icons.label,
                              alwaysExpandedInOffice: true,
                              children: [
                                Wrap(
                                  spacing: spacing.ResponsiveSpacing.getSpacing(
                                    context,
                                    base: displayProvider.isOfficeMode ? 6 : 8,
                                  ),
                                  runSpacing: spacing
                                      .ResponsiveSpacing.getSpacing(
                                    context,
                                    base: displayProvider.isOfficeMode ? 4 : 6,
                                  ),
                                  children:
                                      _expense!.tags!.map((tag) {
                                        return Chip(
                                          label: ResponsiveLabel(tag),
                                          backgroundColor: Colors.grey[200],
                                        );
                                      }).toList(),
                                ),
                              ],
                            ),

                          // Receipt photo section
                          if (_expense!.receiptPhotoUrl != null) ...[
                            Padding(
                              padding: EdgeInsets.symmetric(
                                vertical: spacing.ResponsiveSpacing.getSpacing(
                                  context,
                                  base: 8,
                                ),
                              ),
                              child: const ResponsiveSubtitle(
                                'Receipt',
                                style: TextStyle(fontWeight: FontWeight.bold),
                              ),
                            ),
                            Container(
                              width: double.infinity,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey),
                                borderRadius: BorderRadius.circular(
                                  spacing.ResponsiveSpacing.getBorderRadius(
                                    context,
                                    base: 8,
                                  ),
                                ),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(
                                  spacing.ResponsiveSpacing.getBorderRadius(
                                    context,
                                    base: 8,
                                  ),
                                ),
                                child: Image.network(
                                  _expense!.receiptPhotoUrl!,
                                  fit: BoxFit.cover,
                                  loadingBuilder: (
                                    context,
                                    child,
                                    loadingProgress,
                                  ) {
                                    if (loadingProgress == null) return child;
                                    return SizedBox(
                                      height: 300,
                                      child: Center(
                                        child:
                                            loadingProgress
                                                        .expectedTotalBytes !=
                                                    null
                                                ? QuarterliesProgressIndicator(
                                                  value:
                                                      loadingProgress
                                                          .cumulativeBytesLoaded /
                                                      loadingProgress
                                                          .expectedTotalBytes!,
                                                  label:
                                                      'Loading receipt image...',
                                                  showPercentage: true,
                                                )
                                                : const InlineLoadingIndicator(
                                                  text:
                                                      'Loading receipt image...',
                                                  size: 24.0,
                                                ),
                                      ),
                                    );
                                  },
                                  errorBuilder: (context, error, stackTrace) {
                                    return const SizedBox(
                                      height: 100,
                                      child: Center(
                                        child: Text(
                                          'Failed to load receipt image',
                                        ),
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                          ],

                          // Metadata section
                          AdaptiveDetailSection(
                            title: 'Record Information',
                            icon: Icons.info,
                            alwaysExpandedInOffice: true,
                            children: [
                              if (displayProvider.isOfficeMode) ...[
                                // Office Mode: Compact grid layout
                                Row(
                                  children: [
                                    Expanded(
                                      child: AdaptiveInfoRow(
                                        label: 'Created',
                                        value: DateFormat(
                                          'MM/dd/yyyy',
                                        ).format(_expense!.createdAt),
                                        icon: Icons.calendar_today,
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    Expanded(
                                      child: AdaptiveInfoRow(
                                        label: 'Updated',
                                        value:
                                            _expense!.updatedAt != null
                                                ? DateFormat(
                                                  'MM/dd/yyyy',
                                                ).format(_expense!.updatedAt!)
                                                : 'N/A',
                                        icon: Icons.update,
                                      ),
                                    ),
                                  ],
                                ),
                              ] else ...[
                                // Field Mode: Existing layout
                                Row(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  children: [
                                    Text(
                                      'Created: ${DateFormat('MM/dd/yyyy').format(_expense!.createdAt)}',
                                    ),
                                    Text(
                                      'Updated: ${_expense!.updatedAt != null ? DateFormat('MM/dd/yyyy').format(_expense!.updatedAt!) : 'N/A'}',
                                    ),
                                  ],
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                    );
                  },
                );
          },
        ),
      ),
    );
  }
}
